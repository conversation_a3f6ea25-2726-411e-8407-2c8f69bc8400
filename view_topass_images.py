import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def view_topass_images():
    """查看toPass中的发光按钮图片"""
    topass_dir = Path('./data/button-debug-saves/allButtons/toPass')
    images = list(topass_dir.glob('*.jpg')) + list(topass_dir.glob('*.png'))
    
    print(f"Found {len(images)} images in toPass directory:")
    for img_path in images:
        print(f"- {img_path.name}")
        
        # 读取图片
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"  Failed to load {img_path}")
            continue
            
        # 转换为RGB显示
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 显示图片信息
        print(f"  Image shape: {image.shape}")
        print(f"  Image dtype: {image.dtype}")
        
        # 分析亮度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        mean_brightness = np.mean(gray)
        max_brightness = np.max(gray)
        print(f"  Mean brightness: {mean_brightness:.1f}")
        print(f"  Max brightness: {max_brightness}")
        
        # 分析HSV
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        h_mean = np.mean(hsv[:,:,0])
        s_mean = np.mean(hsv[:,:,1])
        v_mean = np.mean(hsv[:,:,2])
        print(f"  HSV means: H={h_mean:.1f}, S={s_mean:.1f}, V={v_mean:.1f}")
        
        # 检测高亮区域
        highlight_mask = cv2.inRange(hsv, (0, 0, 230), (180, 60, 255))
        highlight_pixels = cv2.countNonZero(highlight_mask)
        total_pixels = image.shape[0] * image.shape[1]
        highlight_ratio = highlight_pixels / total_pixels
        print(f"  Highlight pixels: {highlight_pixels} ({highlight_ratio*100:.1f}%)")
        
        print()

if __name__ == "__main__":
    view_topass_images()
