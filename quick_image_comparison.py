import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd

def quick_brightness_analysis():
    """Quick analysis focusing on brightness differences."""
    
    # Load images
    mustpass_dir = Path('./data/button-debug-saves/knob/mustPass')
    topass_dir = Path('./data/button-debug-saves/knob/toPass')
    
    mustpass_files = list(mustpass_dir.glob('*.png'))
    topass_files = list(topass_dir.glob('*.png'))
    
    print(f"Found {len(mustpass_files)} mustPass images and {len(topass_files)} toPass images")
    
    # Extract brightness statistics
    mustpass_brightness = []
    topass_brightness = []
    
    print("Analyzing mustPass images...")
    for img_path in mustpass_files:
        img = cv2.imread(str(img_path))

        if img is not None:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            mustpass_brightness.append({
                'filename': img_path.name,
                'mean_brightness': np.mean(gray),
                'std_brightness': np.std(gray),
                'min_brightness': np.min(gray),
                'max_brightness': np.max(gray),
                'median_brightness': np.median(gray)
            })
    
    print("Analyzing toPass images...")
    for img_path in topass_files:
        img = cv2.imread(str(img_path))
        # crop to center
        # ---
        # h, w = img.shape[:2]
        # center_x, center_y = w // 2, h // 2
        # crop_w, crop_h = w // 4, h // 4
        # img = img[center_y - crop_h:center_y + crop_h, center_x - crop_w:center_x + crop_w]
        # ---
        if img is not None:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            upper_quartile = np.percentile(gray, 75)
            topass_brightness.append({
                'filename': img_path.name,
                'upper_quartile_brightness': upper_quartile
            })
    
    # Convert to arrays for easy analysis
    mustpass_means = [np.percentile(cv2.cvtColor(cv2.imread(str(mustpass_dir / x['filename'])), cv2.COLOR_BGR2GRAY), 75) for x in mustpass_brightness]
    topass_means = [x['upper_quartile_brightness'] for x in topass_brightness]
    
    # Print statistics
    print("\n" + "="*60)
    print("BRIGHTNESS ANALYSIS RESULTS")
    print("="*60)
    
    print(f"\nmustPass Images (n={len(mustpass_means)}):")
    print(f"  Mean brightness: {np.mean(mustpass_means):.2f} ± {np.std(mustpass_means):.2f}")
    print(f"  Range: {np.min(mustpass_means):.2f} - {np.max(mustpass_means):.2f}")
    print(f"  Median: {np.median(mustpass_means):.2f}")
    
    print(f"\ntoPass Images (n={len(topass_means)}):")
    print(f"  Mean brightness: {np.mean(topass_means):.2f} ± {np.std(topass_means):.2f}")
    print(f"  Range: {np.min(topass_means):.2f} - {np.max(topass_means):.2f}")
    print(f"  Median: {np.median(topass_means):.2f}")
    
    # Calculate difference
    diff = np.mean(mustpass_means) - np.mean(topass_means)
    relative_diff = (diff / np.mean(topass_means)) * 100
    
    print(f"\nDifference:")
    print(f"  Absolute: {diff:.2f}")
    print(f"  Relative: {relative_diff:+.1f}%")
    
    # Suggest threshold
    threshold = (np.mean(mustpass_means) + np.mean(topass_means)) / 2
    print(f"\nSuggested brightness threshold: {threshold:.2f}")
    
    if np.mean(mustpass_means) > np.mean(topass_means):
        print(f"Classification rule: brightness > {threshold:.2f} → mustPass")
    else:
        print(f"Classification rule: brightness < {threshold:.2f} → mustPass")
    
    # Create visualization
    plt.figure(figsize=(12, 8))
    
    # Histogram comparison
    plt.subplot(2, 2, 1)
    plt.hist(mustpass_means, bins=20, alpha=0.7, label='mustPass', color='blue')
    plt.hist(topass_means, bins=20, alpha=0.7, label='toPass', color='red')
    plt.axvline(threshold, color='green', linestyle='--', label=f'Threshold: {threshold:.1f}')
    plt.xlabel('Mean Brightness')
    plt.ylabel('Frequency')
    plt.title('Brightness Distribution Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Box plot
    plt.subplot(2, 2, 2)
    data_to_plot = [mustpass_means, topass_means]
    plt.boxplot(data_to_plot, labels=['mustPass', 'toPass'])
    plt.ylabel('Mean Brightness')
    plt.title('Brightness Box Plot Comparison')
    plt.grid(True, alpha=0.3)
    
    # Scatter plot with index
    plt.subplot(2, 2, 3)
    plt.scatter(range(len(mustpass_means)), mustpass_means, alpha=0.7, label='mustPass', color='blue')
    plt.scatter(range(len(topass_means)), topass_means, alpha=0.7, label='toPass', color='red')
    plt.axhline(threshold, color='green', linestyle='--', label=f'Threshold: {threshold:.1f}')
    plt.xlabel('Image Index')
    plt.ylabel('Mean Brightness')
    plt.title('Brightness Values by Image')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Cumulative distribution
    plt.subplot(2, 2, 4)
    mustpass_sorted = np.sort(mustpass_means)
    topass_sorted = np.sort(topass_means)
    
    plt.plot(mustpass_sorted, np.linspace(0, 1, len(mustpass_sorted)), label='mustPass', color='blue')
    plt.plot(topass_sorted, np.linspace(0, 1, len(topass_sorted)), label='toPass', color='red')
    plt.axvline(threshold, color='green', linestyle='--', label=f'Threshold: {threshold:.1f}')
    plt.xlabel('Mean Brightness')
    plt.ylabel('Cumulative Probability')
    plt.title('Cumulative Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    output_dir = Path('./data/button-debug-saves/knob/analysis')
    output_dir.mkdir(parents=True, exist_ok=True)
    plt.savefig(output_dir / 'quick_brightness_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\nVisualization saved to {output_dir / 'quick_brightness_analysis.png'}")
    
    return {
        'mustpass_stats': {
            'mean': np.mean(mustpass_means),
            'std': np.std(mustpass_means),
            'min': np.min(mustpass_means),
            'max': np.max(mustpass_means),
            'median': np.median(mustpass_means)
        },
        'topass_stats': {
            'mean': np.mean(topass_means),
            'std': np.std(topass_means),
            'min': np.min(topass_means),
            'max': np.max(topass_means),
            'median': np.median(topass_means)
        },
        'threshold': threshold,
        'difference': diff,
        'relative_difference': relative_diff
    }

if __name__ == "__main__":
    results = quick_brightness_analysis()