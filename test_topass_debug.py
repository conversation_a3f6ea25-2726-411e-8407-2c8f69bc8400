import cv2
import numpy as np
from pathlib import Path
from buttonControl.button_detection import detect_buttons_by_status

def test_topass_images():
    """专门测试toPass图片的检测效果"""
    topass_dir = Path('./data/button-debug-saves/allButtons/toPass')
    images = list(topass_dir.glob('*.jpg')) + list(topass_dir.glob('*.png'))
    
    for img_path in images:
        print(f"\n=== Testing {img_path.name} ===")
        
        # 读取图片
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"Failed to load {img_path}")
            continue
            
        # 转换为RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        try:
            # 使用检测函数
            result = detect_buttons_by_status(image_rgb, 'uncertain', verbose=True, display_process=False)

            if result is not None:
                if isinstance(result, tuple) and len(result) == 6:
                    display_img, top_row, bottom_row, knob, handle_angle, mode_code = result
                    print(f"Detection results:")
                    print(f"  Top row: {top_row}")
                    print(f"  Bottom row: {bottom_row}")
                    print(f"  Knob: {knob}")
                    print(f"  Handle angle: {handle_angle}")
                    print(f"  Mode code: {mode_code}")

                    # 检查是否成功检测到4个按钮
                    total_buttons = 0
                    if top_row: total_buttons += len(top_row)
                    if bottom_row: total_buttons += len(bottom_row)

                    success = total_buttons >= 4 and knob is not None
                    print(f"  SUCCESS: {success} (found {total_buttons} buttons, knob: {knob is not None})")
                else:
                    print(f"Detection returned unexpected format: {type(result)}, length: {len(result) if hasattr(result, '__len__') else 'N/A'}")
            else:
                print("Detection failed - no result returned")

        except Exception as e:
            print(f"Error during detection: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_topass_images()
